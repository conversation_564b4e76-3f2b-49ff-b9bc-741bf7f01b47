import Koa from 'koa'
import cors from '@koa/cors'
import Router from 'koa-router'
import bodyParser from 'koa-bodyparser'
import { PORT } from './constants.js'
import { getBlockhash, getBalance, getMultisigs, getTos, createTransfer, getTransactions, buildExecuteInstruction, handleTransactionAction, cancelTransaction } from './service.js'

const app = new Koa()
const router = new Router()

app.use(cors())
app.use(bodyParser())

router.get('/api/blockhash', getBlockhash)
router.post('/api/balance', getBalance)
router.get('/api/tos', getTos)
router.get('/api/multisigs', getMultisigs)
router.post('/api/transfer', createTransfer)
router.post('/api/transactions', getTransactions)
router.post('/api/transactions/build-execute', buildExecuteInstruction)
router.post('/api/transactions/cancel', cancelTransaction)
router.post('/api/transactions/:action', handleTransactionAction)

app.use(router.routes()).use(router.allowedMethods())

async function run () {
  // ubikey检测
  // try {
  //   execSync('gpg --card-status')
  // } catch (err) {
  //   console.log('yubikey设备未连接')
  //   process.exit(1)
  // }

  // 下载并解密文件
  // await downloadFile('tos.bin')
  // await downloadFile('tokens.bin')

  // 解密并加载数据
  // await loadTokens()
  // await loadTos()

  app.listen(PORT, () => console.log(`🚀 Squads Backend Server running on http://localhost:${PORT}`))
}

process.on('uncaughtException', async error => console.error(`uncaughtException. error info: ${error.message}`))
process.on('unhandledRejection', async error => console.error(`unhandledRejection. error info: ${error.message}`))

run()
